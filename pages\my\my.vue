<template>
  <view class="bkcolor">
    <!-- 页面加载状态 -->
    <view v-if="pageLoading" class="page-loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>
    </view>

    <!-- 页面主要内容 -->
    <view v-else>
      <!-- 第一行 - 简化的顶部区域 -->
      <view class="my-top-wrap">
        <!-- 顶部功能栏 -->
        <view class="top-header">
          <view class="function-btn" @click="openScan" v-if="!hideLogin">
            <view class="btn-circle">
              <image :src="`${config.BASE_URL}/static/scan.png`" style="width: 16px; height: 16px"></image>
            </view>
          </view>
          <view class="header-title" v-if="!showWelcomeTitle">E卡畅通ETC</view>
          <view class="placeholder"></view>
        </view>

        <!-- 用户信息卡片 - 简化布局 -->
        <view class="user-info-card" v-if="!hideLogin">
          <view class="user-avatar-section">
            <image :src="userInfo.headPic === '' ? defaultHead : userInfo.headPic" class="user-avatar"
              @click="toSetUserInfo"></image>
          </view>

          <view class="user-details" v-if="isLogin">
            <view class="user-name">{{ userInfo.userName }}</view>
            <view class="balance-info" v-if="showBalanceInfo">
              <text>余额: ¥{{ userInfo.integral }}</text>
              <view class="withdraw-btn" @click="openToast()">提现</view>
            </view>
          </view>

          <view v-else class="login-section" @click="goToLogin">
            <view class="welcome-text">请登录</view>
            <view class="login-hint">点击登录享受更多服务</view>
          </view>
        </view>

        <!-- 隐藏登录模式下的欢迎横幅 -->
        <view class="welcome-banner" v-if="hideLogin">
          <view class="banner-content">

            <view class="banner-text">
              <view class="main-title">便民ETC服务平台</view>
              <view class="sub-title">高速通行 · 便捷查询 · 优质服务</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 第二行 - 修复快捷服务样式 -->
      <view class="quick-services">
        <view class="service-item" @click="getOilUrl">
          <view class="service-icon">
            <image :src="`${config.BASE_URL}/static/cheer.png`" mode="aspectFit" class="service-icon-img"></image>
          </view>
          <view class="service-text">加油服务</view>
        </view>
        <view class="service-item" @click="getRlectricityUrl">
          <view class="service-icon">
            <image :src="`${config.BASE_URL}/static/charge.png`" mode="aspectFit" class="service-icon-img"></image>
          </view>
          <view class="service-text">充电服务</view>
        </view>
      </view>

      <!-- 第三行 - 修复功能菜单样式 -->
      <view class="function-menu">
        <view class="menu-title">便民服务</view>
        <view class="menu-grid">
          <!-- 加载状态 -->
          <view v-if="menuLoading" class="menu-loading">
            <text>菜单加载中...</text>
          </view>
          <!-- 菜单项 -->
          <view v-else v-for="(data, index) in visibleNavTar" :key="index" class="menu-item"
            @click="doMethod(index, data)">
            <view class="menu-icon">
              <uni-icons :type="getIconText(data.iconType)" size="20" color="#666"></uni-icons>
            </view>
            <view class="menu-text">{{ data.text }}</view>
          </view>
        </view>
      </view>

      <!-- 第四行 - 修复产品推荐样式 -->
      <view class="product-section" v-if="shouldShowETCProducts">
        <view class="section-title">ETC产品推荐</view>
        <view class="product-card" v-for="(data, index) in goods" :key="index">
          <view class="product-header">
            <view class="product-info">
              <view class="product-name">{{ data.name }}</view>
              <view class="product-desc">{{ data.msg }}</view>
              <!-- 移除权益状态显示，允许继续购买 -->
            </view>
            <!-- 所有ETC相关商品都显示可点击的购买按钮 -->
            <view class="action-btn" @click="toBuy(data, true)">ETC办理</view>
          </view>

          <!-- 修复服务权益展示样式 -->
          <view class="benefits-row">
            <view class="benefit-item">
              <view class="benefit-icon">
                <image :src="`${config.BASE_URL}/static/ETC.png`" mode="widthFix"></image>
              </view>
              <view class="benefit-text">ETC服务</view>
            </view>
            <view class="benefit-item">
              <view class="benefit-icon">
                <image :src="`${config.BASE_URL}/static/ETC.png`" mode="widthFix"></image>
              </view>
              <view class="benefit-text">ETC商城</view>
            </view>
            <view class="benefit-item">
              <view class="benefit-icon">
                <image :src="`${config.BASE_URL}/static/ETC.png`" mode="widthFix"></image>
              </view>
              <view class="benefit-text">加油服务</view>
            </view>
            <view class="benefit-item">
              <view class="benefit-icon">
                <image :src="`${config.BASE_URL}/static/ETC.png`" mode="widthFix"></image>
              </view>
              <view class="benefit-text">极速办理</view>
            </view>
          </view>
        </view>
      </view>

      <view>
        <!-- 使用原生模态框替代 uni-popup-dialog -->
        <!-- 当需要显示绑定确认时，直接调用 uni.showModal -->
      </view>

      <!-- 固定联系按钮 -->
      <view class="contact-buttons">
        <view class="contact-item" @click="toPhone()">
          <image :src="`${config.BASE_URL}/static/phone.png`" mode="aspectFill"></image>
        </view>
        <view class="contact-item" @click="towxcustomer"
          v-if="config.CUSTOMER_SERVICE_CONFIG.WEWORK_CONFIG.CUSTOMER_SERVICE_URL">
          <image :src="`${config.BASE_URL}/static/customer.png`" mode="aspectFill"></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { isLogin } from "@/utils/auth.js";
import { allcommodity } from "@/api/shop.js";
import { mapGetters } from "vuex";
import {
  updUser,
  showInformations,
} from "@/api/user.js";
import { checkCurrentPageAccess } from "@/utils/routeGuard.js";
import { handlePagePermissionCheck } from "@/utils/permissionChecker.js";
import { getMenuConfig } from "@/api/menu.js";
import config from "@/utils/config.js";
export default {
  data() {
    return {
      config,
      // 菜单配置状态
      menuConfig: [], // 存储从接口获取的菜单配置
      hideLogin: false, // 是否隐藏登录功能
      hideTabBar: false, // 是否隐藏底部导航栏
      showWelcomeTitle: false, // 是否显示欢迎标题
      shouldShowETCProducts: true, // 是否显示ETC产品推荐区域
      navTar: [
        {
          text: "ETC发票",
          iconType: "paperplane-filled",
          click: () => {
            console.log("点击ETC发票");
            // 直接跳转到ETC发票页面（原通行记录功能）
            uni.navigateTo({
              url: "/subpackages/services/pages/ETCService/ETCService",
            });
          },
        },
        {
          text: "我的订单",
          iconType: "list",
          click: () => {
            uni.navigateTo({
              url: "/subpackages/user/pages/myOrder/myOrder",
            });
          },
        },
        {
          text: "充值服务",
          iconType: "phone-filled",
          click: () => {
            console.log("点击充值服务");
            uni.navigateTo({
              url: "/subpackages/common/pages/creditWebview/creditWebview?page=warning",
            });
            return;
          },
        },
        {
          text: "ETC办理",
          iconType: "gear-filled",
          click: () => {
            // this.toUrl()
            uni.navigateTo({
              url: "/subpackages/user/pages/my/etcHandle",
            });
          },
        },
        {
          text: "通行记录",
          iconType: "navigate-filled",
          click: () => {
            console.log("点击通行记录");
            // 跳转到新的通行记录页面
            uni.navigateTo({
              url: "/subpackages/services/pages/PassageRecord/PassageRecord",
            });
          },
        },
        {
          text: "ETC资格查询",
          iconType: "search",
          click: () => {
            console.log("点击ETC资格查询");
            this.goToETCQualificationQuery();
          },
        },
      ],
      visibleNavTar: [], // 可见的菜单项
      menuLoading: true, // 菜单加载状态
      pageLoading: true, // 页面整体加载状态
      isLogin: false,
      defaultHead: `${config.BASE_URL}/static/photo.png`,
      goods: [],
      isSend: false,
      msgType: "success",
      myInvitationCode: "",
      etcFlag: false, // 控制ETC会员权限的标志
      etcStatus: "", // ETC权益状态：1表示已有权益
    };
  },
  computed: {
    ...mapGetters("user", ["userInfo"]),
  },
  methods: {
    // 图标映射方法 - 返回uni-icons图标名称
    getIconText(iconType) {
      const iconMap = {
        "paperplane-filled": "paperplane-filled",
        list: "list",
        "phone-filled": "phone-filled",
        "gear-filled": "gear-filled",
        "navigate-filled": "navigate-filled",
        search: "search",
      };
      return iconMap[iconType] || "circle";
    },

    // 获取菜单项显示文本，统一显示ETC办理
    getMenuText(item) {
      if (item.iconType === "gear-filled") {
        return "ETC办理";
      }
      return item.text;
    },

    // 检查登录状态的通用方法
    checkLoginStatus() {
      if (!isLogin()) {
        // 跳转到登录页面而不是显示toast
        uni.navigateTo({
          url: "/pages/unifiedLogin/unifiedLogin",
        });
        return false;
      }
      return true;
    },

    // 获取菜单配置并过滤显示
    async getMenuConfigAndFilter() {
      try {
        this.menuLoading = true;
        const res = await getMenuConfig();

        if (res.data && res.data.code === 200) {
          const menuConfig = res.data.data || [];
          this.menuConfig = menuConfig;

          // 处理隐藏登录功能
          this.processHideLoginLogic(menuConfig);

          // 根据接口配置过滤菜单项
          this.visibleNavTar = this.navTar.filter((navItem) => {
            return this.shouldShowMenuItem(navItem.text, menuConfig);
          });

          // 检查是否显示ETC产品推荐区域
          this.shouldShowETCProducts = this.shouldShowMenuItem("ETC产品推荐", menuConfig);

          console.log("菜单配置获取成功", menuConfig);
          console.log("过滤后的菜单", this.visibleNavTar);
          console.log("ETC产品推荐显示状态:", this.shouldShowETCProducts);
          console.log("隐藏登录状态:", this.hideLogin);
        } else {
          // 接口调用失败，显示所有菜单项
          this.visibleNavTar = [...this.navTar];
          console.warn("菜单配置获取失败，显示默认菜单");
        }
      } catch (error) {
        console.error("获取菜单配置出错:", error);
        // 出错时显示所有菜单项
        this.visibleNavTar = [...this.navTar];
        uni.showToast({
          title: "菜单加载失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.menuLoading = false;
        this.pageLoading = false;
      }
    },

    // 处理隐藏登录相关逻辑
    processHideLoginLogic(menuConfig) {
      this.hideLogin = false;
      this.hideTabBar = false;
      this.showWelcomeTitle = false;

      // 查找隐藏登录配置项
      const hideLoginConfig = menuConfig.find(config => config.cornName === '隐藏登录');
      if (hideLoginConfig) {
        // status为0时启用隐藏登录模式，status为1时正常显示登录
        if (hideLoginConfig.status === 0) {
          this.hideLogin = true;
          this.hideTabBar = true;
          this.showWelcomeTitle = true;
          console.log("检测到隐藏登录配置，启用隐藏登录模式");
        } else {
          console.log("隐藏登录配置为正常显示模式");
        }
      }

      // 如果需要隐藏底部导航栏，动态设置
      if (this.hideTabBar) {
        this.setTabBarVisibility(false);
      }
    },

    // 判断菜单项是否应该显示
    shouldShowMenuItem(itemText, menuConfig) {
      // 首先查找精确匹配的配置项
      const exactMatch = menuConfig.find(config => config.cornName === itemText);
      if (exactMatch) {
        // 如果找到精确匹配的配置，status为1时显示，status为0时隐藏
        return exactMatch.status === 1;
      }

      // 兼容原有的"-"分割逻辑（向后兼容）
      for (const config of menuConfig) {
        if (config.cornName && config.cornName.includes('-')) {
          // 处理用"-"分割的多个按钮组合
          const buttonNames = config.cornName.split('-');
          if (buttonNames.includes(itemText)) {
            // 如果status为0，则隐藏该按钮
            return config.status === 1;
          }
        }
      }

      // 如果没有找到对应配置，默认显示
      return true;
    },

    // 设置底部导航栏显示/隐藏
    setTabBarVisibility(show) {
      if (show) {
        uni.showTabBar({
          animation: false
        });
      } else {
        uni.hideTabBar({
          animation: false
        });
      }
    },

    dialogClose() {
      console.log("点击关闭");
    },
    dialogConfirm() {
      console.log("点击确认，开始绑定");
      // 修改用户信息
      let data = {
        invitationCode: this.myInvitationCode,
        id: this.userInfo.id,
      };
      updUser(data)
        .then(() => {
          console.log("绑定成功");
          uni.showToast({
            icon: "success",
            title: "绑定成功",
          });
          this.haveInvitationCode = false;
        })
        .catch(() => {
          uni.showToast({
            icon: "error",
            title: "绑定失败",
          });
        });
    },

    // 跳转个人信息
    toSetUserInfo() {
      // 在隐藏登录模式下，跳转到登录页面
      if (this.hideLogin) {
        uni.navigateTo({
          url: "/pages/unifiedLogin/unifiedLogin",
        });
        return;
      }

      if (!this.checkLoginStatus()) return;
      uni.navigateTo({
        url: "/subpackages/user/pages/setUserInfo/setUserInfo",
      });
    },

    // 跳转到登录页面
    goToLogin() {
      uni.navigateTo({
        url: "/pages/unifiedLogin/unifiedLogin",
      });
    },

    // 打开二维码
    openScan() {
      if (!this.checkLoginStatus()) return;
      uni.scanCode({
        success: (res) => {
          console.log('[Scan] 扫码结果:', res);
          this.handleScanResult(res.path);
        },
        fail(err) {
          console.error('扫码失败:', err);
          uni.showToast({
            title: "扫码失败",
            icon: "none"
          });
        }
      });
    },

    // 处理扫码结果 - 优化后的统一处理方法
    async handleScanResult(url) {
      if (!url) {
        uni.showToast({
          title: "无效的二维码",
          icon: "none"
        });
        return;
      }

      // 提取scene参数
      const sceneMatch = url.match(/scene=([^&]+)/);
      if (!sceneMatch) {
        uni.showToast({
          title: "无法识别的二维码格式",
          icon: "none"
        });
        return;
      }

      const sceneValue = decodeURIComponent(sceneMatch[1]);
      console.log('[Scan] 解析的scene值:', sceneValue);

      // 使用工具函数解析邀请码类型
      const { parseInvitationCode, INVITATION_CODE_TYPES } = await import('@/utils/invitationCodeHelper.js');
      const parsed = parseInvitationCode(sceneValue);

      if (parsed.type === INVITATION_CODE_TYPES.PROXY_REGISTER) {
        // 代理注册二维码
        this.handleProxyRegisterQR(sceneValue);
      } else if (parsed.type === INVITATION_CODE_TYPES.USER) {
        // 普通用户邀请码
        this.handleUserInvitationQR(parsed.data.invitationCode);
      } else {
        uni.showToast({
          title: "无法识别的二维码类型",
          icon: "none"
        });
      }
    },

    // 处理代理注册二维码
    handleProxyRegisterQR(sceneValue) {
      console.log('[Scan] 检测到代理注册二维码，跳转到代理注册页面');
      uni.navigateTo({
        url: `/pages/proxyRegister/register?scene=${encodeURIComponent(sceneValue)}`
      });
    },

    // 处理用户邀请码二维码
    async handleUserInvitationQR(invitationCode) {
      console.log('[Scan] 检测到用户邀请码，开始绑定:', invitationCode);

      const userId = uni.getStorageSync("userId");
      if (!userId) {
        uni.showToast({
          title: "请先登录",
          icon: "error"
        });
        return;
      }

      // 显示加载状态
      uni.showLoading({
        title: "绑定中...",
        mask: true
      });

      try {
        // 使用工具函数处理邀请码绑定
        const { storeInvitationCode, bindUserInvitationCode, showBindingResult } = await import('@/utils/invitationCodeHelper.js');

        // 更新本地存储的邀请码
        storeInvitationCode(invitationCode);

        // 绑定邀请码
        const result = await bindUserInvitationCode(userId, invitationCode);

        // 显示绑定结果
        showBindingResult(result);

        // 绑定成功后刷新用户信息
        if (result.success) {
          this.$store.dispatch("user/getUserInfo");
        }
      } catch (error) {
        console.error('[Scan] 邀请码绑定失败:', error);
        uni.showToast({
          icon: "error",
          title: "网络错误，绑定失败",
        });
      } finally {
        uni.hideLoading();
      }
    },
    // 获取商品
    allcommodity() {
      // 修改：未登录时也要获取商品数据，让审核人员看到产品推荐
      allcommodity().then((res) => {
        if (res.data.code !== 200) {
          return;
        }
        this.goods = res.data.data;
      }).catch((error) => {
        console.error("获取商品数据失败:", error);
        // 获取失败时不影响页面显示
      });
    },
    // 跳转快递
    toUrl() {
      if (!this.checkLoginStatus()) return;
      const expressConfig = config.MINI_PROGRAM_JUMP_CONFIG.EXPRESS_MINI_PROGRAM;
      let toPath =
        `pages/empty_hair/new_module/select_etc_handle_type/select_etc_handle_type?isNewTrucks=0&shopId=${expressConfig.SHOP_ID}&vehPlates=` +
        this.userInfo.carId +
        `&extend={"merchantCode":"${expressConfig.MERCHANT_CODE}"}`;
      uni.navigateToMiniProgram({
        // appid  写你要跳转的小程序的 appid
        appId: expressConfig.APP_ID,
        // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
        path: toPath,
        extraData: {
          type: "out",
        },
        // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
        envVersion: expressConfig.ENV_VERSION,
        success() {
          // 打开成功
          uni.showToast({
            title: "跳转成功",
          });
        },
        fail() {
          // 打开失败/取消
        },
      });
    },
    // 获取链接
    getOilUrl() {
      // 修改：加油服务允许未登录用户使用
      const oilConfig = config.MINI_PROGRAM_JUMP_CONFIG.OIL_MINI_PROGRAM;
      uni.navigateToMiniProgram({
        // appid  写你要跳转的小程序的 appid
        appId: oilConfig.APP_ID,
        // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
        path: oilConfig.PATH,
        extraData: {
          type: "out",
        },
        // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
        envVersion: oilConfig.ENV_VERSION,
        success() {
          // 打开成功
          uni.showToast({
            title: "跳转成功",
          });
        },
        fail() {
          // 打开失败/取消
        },
      });
    },
    // 购买商品
    toBuy(goods, isFromETCRecommendation = false) {
      console.log('[toBuy] 调用参数:', { goods, isFromETCRecommendation });

      // ETC办理功能允许未登录用户使用
      const isETCService = goods && (goods.name && goods.name.includes('ETC'));

      // 在隐藏登录模式下，ETC办理功能仍可使用，其他跳转到登录页面
      if (this.hideLogin && !isETCService && !isFromETCRecommendation) {
        uni.navigateTo({
          url: "/pages/unifiedLogin/unifiedLogin",
        });
        return;
      }

      // ETC办理功能不需要登录检查，其他功能需要
      if (!isETCService && !isFromETCRecommendation && !this.checkLoginStatus()) return;

      // 新增：ETC权益购买需要邀请码验证
      if (isETCService || isFromETCRecommendation) {
        console.log('[ETC权益验证] 检测到ETC服务或来自ETC推荐，商品信息:', goods);
        console.log('[ETC权益验证] isETCService:', isETCService);
        console.log('[ETC权益验证] isFromETCRecommendation:', isFromETCRecommendation);

        // 从allcommodity接口获取的商品都是权益商品，需要邀请码验证
        const { getStoredInvitationCode } = require('@/utils/invitationCodeHelper.js');
        const invitationCode = getStoredInvitationCode();

        console.log('[ETC权益验证] 获取到的邀请码:', invitationCode);
        console.log('[ETC权益验证] 本地存储的原始邀请码:', uni.getStorageSync("invitationCode"));
        console.log('[ETC权益验证] 商品名称:', goods?.name);

        if (!invitationCode) {
          console.log('[ETC权益验证] 没有邀请码，显示提示');
          uni.showToast({
            title: "请扫描渠道码进行办理",
            icon: "none",
            duration: 2000
          });
          return;
        } else {
          console.log('[ETC权益验证] 有邀请码，继续购买流程');
        }
      }

      console.log('[toBuy] 准备跳转，商品信息:', goods);
      uni.navigateTo({
        url:
          "/subpackages/user/pages/bindingCarId/bindingCarId?goods=" +
          JSON.stringify(goods),
      });
    },
    // 获取电url
    getRlectricityUrl() {
      // 修改：充电服务允许未登录用户使用
      const chargingConfig = config.MINI_PROGRAM_JUMP_CONFIG.CHARGING_MINI_PROGRAM;
      uni.navigateToMiniProgram({
        // appid  写你要跳转的小程序的 appid
        appId: chargingConfig.APP_ID,
        // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
        path: chargingConfig.PATH,
        extraData: {
          type: "out",
        },
        // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
        envVersion: chargingConfig.ENV_VERSION,
        success() {
          // 打开成功
          uni.showToast({
            title: "跳转成功",
          });
        },
        fail() {
          // 打开失败/取消
        },
      });
    },

    // ETC资格查询 - 跳转到湖南高速ETC小程序
    goToETCQualificationQuery() {
      // 移除登录检查，允许未登录用户使用

      uni.showModal({
        title: "提示",
        content: "是否跳转到湖南高速ETC小程序进行资格查询？",
        success: (res) => {
          if (res.confirm) {
            // 使用微信原生API通过shortLink跳转小程序
            wx.navigateToMiniProgram({
              shortLink: "#小程序://湖南高速ETC/Z9jtyHJg7hrnNFw", // 目标小程序链接
              envVersion: "release", // develop开发版；trial体验版；release正式版
              success: (res) => {
                console.log("跳转湖南高速ETC小程序成功", res);
                uni.showToast({
                  title: "跳转成功",
                  icon: "success",
                });
              },
              fail: (err) => {
                console.error("跳转湖南高速ETC小程序失败", err);
                uni.showToast({
                  title: "跳转失败，请检查是否已安装微信",
                  icon: "none",
                  duration: 2000,
                });
              },
            });
          }
        },
      });
    },
    //执行方法
    doMethod(i, menuItem) {
      // 定义不需要登录的菜单项
      const noLoginRequiredMenus = ["ETC办理", "通行记录", "ETC资格查询", "ETC发票"];
      const isNoLoginRequired = menuItem && noLoginRequiredMenus.includes(menuItem.text);

      // 在隐藏登录模式下，除了特殊菜单项，其他都跳转到登录页面
      if (this.hideLogin && !isNoLoginRequired) {
        uni.navigateTo({
          url: "/pages/unifiedLogin/unifiedLogin",
        });
        return;
      }

      // 对于不需要登录的菜单项，直接执行
      if (isNoLoginRequired) {
        if (menuItem && menuItem.click && typeof menuItem.click === "function") {
          menuItem.click();
        }
        return;
      }

      // 其他菜单项需要检查登录状态
      if (!this.checkLoginStatus()) return;

      // 如果未开通会员服务，阻止功能调用
      if (!this.etcFlag) {
        uni.showToast({
          title: "请开通会员服务",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      // 如果传入了menuItem，直接调用其click方法
      if (menuItem && menuItem.click && typeof menuItem.click === "function") {
        menuItem.click();
        return;
      }

      // 兼容旧的索引方式（保留原有逻辑）
      if (i === 0) {
        if (!this.checkLoginStatus()) return;
        uni.navigateToMiniProgram({
          // appid  写你要跳转的小程序的 appid
          appId: "wx9040bb0d3f910004",
          // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
          path: "pages/index",
          extraData: {
            type: "out",
          },
          // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
          envVersion: "release",
          success() {
            // 打开成功
            uni.showToast({
              title: "跳转成功",
            });
          },
          fail() {
            // 打开失败/取消
          },
        });
      } else if (i === 1) {
        uni.navigateTo({
          url: "/subpackages/user/pages/myOrder/myOrder",
        });
      } else if (i === 2) {
        // 验证是否有权限
        console.log("点击充值服务");
        uni.navigateTo({
          url: "/subpackages/common/pages/creditWebview/creditWebview?page=warning",
        });
        return;
      } else if (i === 3) {
        // this.toUrl()
        uni.navigateTo({
          url:
            "/subpackages/user/pages/my/etcHandle?carId=" + this.userInfo.carId,
        });
      } else {
        uni.showToast({
          title: "暂未开放",
          icon: "error",
        });
      }
    },

    openToast() {
      // 在隐藏登录模式下，跳转到登录页面
      if (this.hideLogin) {
        uni.navigateTo({
          url: "/pages/unifiedLogin/unifiedLogin",
        });
        return;
      }

      uni.showModal({
        title: "提示",
        content: "您尚未达到36个使用月，暂时无法领取",
        showCancel: false,
        confirmText: "确定",
      });
    },


    toPhone() {
      uni.makePhoneCall({
        phoneNumber: config.CUSTOMER_SERVICE_CONFIG.PHONE_NUMBER, //要拨打的手机号
        success: () => {
          console.log("跳转通话");
        },
        fail: () => {
          console.log("取消通话");
        },
      });
    },
    towxcustomer() {
      try {
        wx.openCustomerServiceChat({
          extInfo: {
            url: config.CUSTOMER_SERVICE_CONFIG.WEWORK_CONFIG.CUSTOMER_SERVICE_URL, //客服ID
          },
          corpId: config.CUSTOMER_SERVICE_CONFIG.WEWORK_CONFIG.CORP_ID, //企业微信ID
          success() { },
        });
      } catch (error) {
        showToast("请更新至微信最新版本");
      }
    },
  },
  async onLoad(options) {
    // 处理邀请码 - 使用统一的工具函数
    const { handlePageInvitationCode } = await import('@/utils/invitationCodeHelper.js');
    handlePageInvitationCode(options, 'My');

    // 检查页面访问权限
    if (!checkCurrentPageAccess()) {
      return; // 如果无权限访问，路由守卫会自动重定向
    }

    // 严格的权限检查：基于adminRank的用户端页面权限验证
    if (!handlePagePermissionCheck("user", "用户端首页")) {
      return; // 权限检查失败，会自动重定向
    }

    console.log(options);

    try {
      // 首先获取菜单配置，这决定了页面的显示模式
      await this.getMenuConfigAndFilter();

      // 使用更可靠的登录状态检查
      this.isLogin = isLogin();

      // 只有在已登录状态下且未隐藏登录功能时才获取用户信息
      if (this.isLogin && !this.hideLogin) {
        try {
          // 获取个人信息
          await this.$store.dispatch("user/getUserInfo");
          // 获取并更新 etcFlag 和 etcStatus 状态
          const res = await showInformations();
          if (res.data && res.data.code === 200) {
            this.etcFlag = res.data.data.etcFlag || false;
            this.etcStatus = res.data.data.etcStatus || "";
          }
        } catch (error) {
          console.error("获取用户信息失败:", error);
          if (error.message === "登录状态无效") {
            this.isLogin = false;
          }
        }
      }

      // 获取商品信息
      this.allcommodity();

      console.log("页面加载完成，隐藏登录模式:", this.hideLogin);
    } catch (error) {
      console.error("页面初始化失败:", error);
      // 即使出错也要隐藏加载状态
      this.pageLoading = false;
    }
  },
  async onShow() {
    this.isLogin = isLogin();

    // 确保底部导航栏状态正确
    if (this.hideTabBar) {
      this.setTabBarVisibility(false);
    } else {
      this.setTabBarVisibility(true);
    }

    if (uni.getStorageSync("pay_type_info") === 1) {
      uni.removeStorageSync("pay_type_info");
      uni.showToast({
        title: "充值成功",
      });
    }
    // 只有在已登录状态下且未隐藏登录功能时才刷新用户信息
    if (this.isLogin && !this.hideLogin) {
      await this.$store.dispatch("user/getUserInfo");
      // 刷新 etcFlag 和 etcStatus 状态
      try {
        const res = await showInformations();
        if (res.data && res.data.code === 200) {
          this.etcFlag = res.data.data.etcFlag || false;
          this.etcStatus = res.data.data.etcStatus || "";
        }
      } catch (error) {
        console.error("获取ETC权限状态失败:", error);
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
    // 是否显示提现和积分相关内容
    showBalanceInfo() {
      return this.etcFlag;
    },
  },
  watch: {
    isSend(c) {
      if (c) {
        uni.showLoading({
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },
};
</script>

<style lang="scss">
.bkcolor {
  background: #f5f5f5;
  min-height: 100vh;
}

// 更新顶部样式 - 使用统一主题色
.my-top-wrap {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding-top: 50px;
  border-bottom-left-radius: 25px;
  border-bottom-right-radius: 25px;
  padding-bottom: 25px;
}

.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px 15px;

  .function-btn {
    .btn-circle {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 0.4);
      }
    }
  }

  .header-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  .placeholder {
    width: 40px;
  }
}

// 更新用户信息卡片 - 统一主题风格
.user-info-card {
  background: white;
  margin: 0 20px;
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;

  .user-avatar-section {
    margin-right: 20px;

    .user-avatar {
      width: 70px;
      height: 70px;
      border-radius: 20px;
      border: 3px solid #00d4aa;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .user-details {
    flex: 1;

    .user-name {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      letter-spacing: 0.5px;
    }

    .balance-info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      text {
        color: #666;
        font-size: 15px;
        font-weight: 500;
      }

      .withdraw-btn {
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  .login-section {
    flex: 1;
    text-align: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.2s ease;

    &:active {
      background-color: rgba(0, 212, 170, 0.1);
    }

    .welcome-text {
      font-size: 16px;
      color: #333;
      margin-bottom: 4px;
      font-weight: 500;
    }

    .login-hint {
      font-size: 12px;
      color: #999;
    }

    .login-btn {
      background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
      color: white;
      border: none;
      border-radius: 25px;
      padding: 12px 30px;
      font-size: 15px;
      font-weight: 500;
      transition: all 0.3s ease;
      letter-spacing: 0.5px;

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

// 更新快捷服务样式
.quick-services {
  padding: 25px 20px;
  display: flex;
  gap: 15px;

  .service-item {
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 30px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
    min-height: 90px;
    transition: all 0.3s ease;

    &:active {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 212, 170, 0.15);
    }

    .service-icon {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg,
          rgba(0, 212, 170, 0.1) 0%,
          rgba(0, 163, 137, 0.1) 100%);
      border-radius: 15px;

      image {
        width: 28px;
        height: 28px;
      }

      .service-icon-img {
        width: 28px;
        height: 28px;
        object-fit: contain;
      }
    }

    .service-text {
      font-size: 15px;
      color: #333;
      font-weight: 600;
      text-align: center;
      line-height: 1.2;
      letter-spacing: 0.3px;
    }
  }
}

// 更新功能菜单样式
.function-menu {
  margin: 0 20px 25px;

  .menu-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 18px;
    letter-spacing: 0.5px;
  }

  .menu-grid {
    background: white;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
    display: flex;
    flex-wrap: wrap;
    justify-content: start;
    gap: 15px;

    .menu-loading {
      width: 100%;
      text-align: center;
      padding: 40px 0;
      color: #999;
      font-size: 15px;
      font-weight: 500;
    }

    .menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      text-align: center;
      padding: 12px 4px;
      border-radius: 15px;
      transition: all 0.3s ease;
      width: calc(25% - 12px);
      min-width: 60px;
      min-height: 85px;
      /* 设置固定的最小高度确保对齐 */
      box-sizing: border-box;

      &:active {
        background: rgba(0, 212, 170, 0.05);
        transform: scale(0.95);
      }

      .menu-icon {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg,
            rgba(0, 212, 170, 0.1) 0%,
            rgba(0, 163, 137, 0.1) 100%);
        border-radius: 12px;
        flex-shrink: 0;
        /* 防止图标被压缩 */

        image {
          width: 22px;
          height: 22px;
          object-fit: contain;
        }
      }

      .menu-text {
        font-size: 12px;
        color: #333;
        font-weight: 600;
        line-height: 1.3;
        /* 调整行高为1.3，更好的文字显示 */
        letter-spacing: 0.1px;
        text-align: center;
        max-width: 100%;
        height: 31px;
        /* 精确计算：12px * 1.3 * 2 ≈ 31px，确保能容纳2行文字 */
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
      }
    }
  }
}

// 更新产品推荐样式
.product-section {
  margin: 0 20px 120px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 18px;
    letter-spacing: 0.5px;
  }

  .product-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
    transition: all 0.3s ease;

    &:active {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(0, 212, 170, 0.15);
    }

    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;

      .product-info {
        flex: 1;

        .product-name {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
          letter-spacing: 0.3px;
        }

        .product-desc {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }

      .action-btn {
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        letter-spacing: 0.3px;

        &:active {
          transform: scale(0.95);
        }
      }
    }

    .benefits-row {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;

      .benefit-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        flex: 1;
        padding: 12px 6px;
        border-radius: 12px;
        transition: all 0.3s ease;

        &:active {
          background: rgba(0, 212, 170, 0.05);
          transform: scale(0.95);
        }

        .benefit-icon {
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg,
              rgba(0, 212, 170, 0.1) 0%,
              rgba(0, 163, 137, 0.1) 100%);
          border-radius: 10px;

          image {
            width: 24px;
            height: 24px;
            object-fit: contain;
          }
        }

        .benefit-text {
          font-size: 12px;
          color: #666;
          font-weight: 600;
          line-height: 1.2;
          word-break: keep-all;
          white-space: nowrap;
          letter-spacing: 0.2px;
        }
      }
    }
  }
}

// 更新联系按钮样式
.contact-buttons {
  position: fixed;
  right: 10px;
  bottom: 10vh;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .contact-item {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.9);
      box-shadow: 0 6px 20px rgba(0, 212, 170, 0.3);
    }

    image {
      width: 100%;
      height: 100%;
    }
  }
}

// 添加全局按钮活跃状态
button:active,
.login-btn:active,
.withdraw-btn:active,
.action-btn:active {
  opacity: 0.9;
}

// 添加加载状态样式
.menu-loading,
.loading {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

/* Custom icon styles */
.custom-icon {
  font-size: 24px;
  display: inline-block;
}

/* ETC权益状态样式 */
.benefit-status {
  display: flex;
  align-items: center;
  margin-top: 8px;

  .benefit-text {
    margin-left: 4px;
    font-size: 12px;
    color: #00d4aa;
    font-weight: 500;
  }
}

.benefit-btn {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: #fff !important;
  border: 1px solid #00d4aa;

  &:active {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  }
}

.fire-icon {
  color: #ff6b35;
  font-size: 24px;
}

.star-icon {
  color: #4ecdc4;
  font-size: 24px;
}

/* 页面加载样式 */
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #e0e0e0;
      border-top: 3px solid #00d4aa;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    .loading-text {
      font-size: 14px;
      color: #666;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 隐藏登录模式样式 */
.welcome-banner {
  margin: 15px 10px;
  border-radius: 12px;
  padding: 20px;

  .banner-content {
    display: flex;
    align-items: center;

    .banner-text {
      flex: 1;

      .main-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 6px;
      }

      .sub-title {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
      }
    }
  }
}
</style>
