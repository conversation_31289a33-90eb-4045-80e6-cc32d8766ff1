// 微信小程序配置
const BASE_URL = "https://www.ylst-etc.cn/api";  
// const BASE_URL = "https://drtjza50.beesnat.com";

// 环境判断
const isProduction = process.env.NODE_ENV === "production";
const isDevelopment = !isProduction;

// API超时配置
const API_TIMEOUT = 15000;

// ===== 客服配置 =====
const CUSTOMER_SERVICE_CONFIG = {
  // 客服电话
  PHONE_NUMBER: "4000067882",
  // 企业微信配置
  WEWORK_CONFIG: {
    // 企业微信ID
    CORP_ID: "",
    // 客服ID URL
    CUSTOMER_SERVICE_URL: "",
  },
};

// ===== 第三方小程序跳转配置 =====
const MINI_PROGRAM_JUMP_CONFIG = {
  // 快递小程序
  EXPRESS_MINI_PROGRAM: {
    APP_ID: "wxddb3eb32425e4a96",
    ENV_VERSION: "release",
    MERCHANT_CODE: "PPVMZIYXEXGDRCZOG",
    SHOP_ID: "1303036541465796608",
  },
  // 充电小程序
  CHARGING_MINI_PROGRAM: {
    APP_ID: "wx01440835f1987e8b",
    ENV_VERSION: "release",
    PATH: "pages/index/index",
  },
  // 加油小程序
  OIL_MINI_PROGRAM: {
    APP_ID: "wx1f1ea04b716771be",
    ENV_VERSION: "release",
    PATH: "pages/index",
  },
};

export default {
  BASE_URL,
  isProduction,
  isDevelopment,
  API_TIMEOUT,
  DEBUG: isDevelopment,
  CUSTOMER_SERVICE_CONFIG,
  MINI_PROGRAM_JUMP_CONFIG,
};

export { BASE_URL, isProduction, isDevelopment, API_TIMEOUT };
