import { post, BASE_URL } from "@/utils/requset.js";
import { getToken } from "@/utils/auth.js";

const sendCode = function (data) {
  return post("/login/sendCode", data);
};

const checkLogin = function (data) {
  return post("/login/checkAgentLogin", data);
};

const showInformations = function () {
  return post("/userinformation/showInformations");
};

// 统一处理 showInformations 接口响应的工具函数
const handleShowInformationsResponse = function (res) {
  if (!res || !res.data) {
    throw new Error("接口响应数据格式错误");
  }

  // 检查是否是 token 过期的情况
  if (res.data.code === 2001 && res.data.message === "获取信息失败") {
    console.warn("[ProxyAPI] showInformations Token已过期，需要重新登录");
    throw new Error("登录已过期");
  }

  // 检查其他错误情况
  if (res.data.code !== 200) {
    throw new Error(res.data.message || "获取用户信息失败");
  }

  return res.data.data;
};

const setUserName = function (data) {
  return post("/userinformation/updInfo", data);
};

// 上传图片接口
const uploadImage = function (filePath) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: BASE_URL + "/selfoperatedgoods/common/upload",
      filePath: filePath,
      name: "file",
      header: {
        token: getToken(),
      },
      success: (res) => {
        const data = JSON.parse(res.data);
        resolve({ data });
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

export { sendCode, checkLogin, showInformations, handleShowInformationsResponse, setUserName, uploadImage };
