import { post, get, BASE_URL } from "@/utils/requset.js";
import { getToken } from "@/utils/auth.js";

const showInformations = function () {
  return post("/userinformation/showInformations");
};

// 统一处理 showInformations 接口响应的工具函数
const handleShowInformationsResponse = function (res) {
  if (!res || !res.data) {
    throw new Error("接口响应数据格式错误");
  }

  // 检查是否是 token 过期的情况
  if (res.data.code === 2001 && res.data.message === "获取信息失败") {
    console.warn("[API] showInformations Token已过期，需要重新登录");
    throw new Error("登录已过期");
  }

  // 检查其他错误情况
  if (res.data.code !== 200) {
    throw new Error(res.data.message || "获取用户信息失败");
  }

  return res.data.data;
};

const setUserName = function (data) {
  return post("/userinformation/updInfo", data);
};
const updUser = function (data) {
  return post("/userinformation/updT", data);
};

// 检查是否有权限进入各各权益
const ifGivePage = function () {
  return post("/gly/ifGivePage");
};

// 微信一键获取手机号
const getPhoneNoInfo = function (data) {
  return post("/WxGetInfo/getPhoneNoInfo", data);
};

// 绑定邀请码
const checkUserInvitation = function (data) {
  return post("/userinformation/checkUserInvitation", data);
};

// 获取权益商城订单
const getqyscOrder = function (data) {
  return get("/qyscfinishorder/list", data);
};

// 用户手机号登录
const userPhoneLogin = function (data) {
  console.log("[API] userPhoneLogin 请求参数:", data);
  return post("/login/userPhoneLogin", data);
};

// 上传图片接口
const uploadImage = function (filePath) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: BASE_URL + "/selfoperatedgoods/common/upload",
      filePath: filePath,
      name: "file",
      header: {
        token: getToken(),
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          resolve({ data });
        } catch (e) {
          reject(new Error("响应数据解析失败"));
        }
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

export {
  showInformations,
  handleShowInformationsResponse,
  setUserName,
  ifGivePage,
  updUser,
  getPhoneNoInfo,
  checkUserInvitation,
  getqyscOrder,
  userPhoneLogin,
  uploadImage,
};
